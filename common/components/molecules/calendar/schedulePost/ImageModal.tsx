'use client'

import {
  useState,
} from 'react';
import { CanvasEditor } from '@/common/components/organisms';
import toast from 'react-hot-toast';

interface ImageModalProps {
  isOpen: boolean;
  onClose: () => void;
  agentId: string;
  planId: string;
  content?: string;
  onImageAttached: (imageUrl: string, isFromAI: boolean, file?: File) => void;
}

export const ImageModal = ({
  isOpen,
  onClose,
  agentId,
  planId,
  onImageAttached,
}: ImageModalProps) => {
  const [currentImage, setCurrentImage] = useState('');

  const handleCanvasEditorSave = async (imageUrl: string) => {
    if (imageUrl) {
      try {
        const response = await fetch(imageUrl);
        const blob = await response.blob();
        const timestamp = Date.now();
        const fileName = `canvas-design-${timestamp}.png`;
        const file = new File([blob], fileName, { type: 'image/png' });

        onImageAttached(imageUrl, false, file);
        setCurrentImage(imageUrl);
        onClose();
        toast.success('Media saved!');
      } catch (error) {
        console.error('Error processing canvas image:', error);
        toast.error('Failed to save media');
      }
    } else {
      onClose();
    }
  };

  return (
    <CanvasEditor
      isOpen={isOpen}
      onClose={() => onClose()}
      onSave={handleCanvasEditorSave}
      initialImage={currentImage || undefined}
      agentId={agentId}
      planId={planId}
    />
  );
};

export default ImageModal;
